<!doctype html>
<html class="no-js" lang="zxx" dir="ltr">
<head>
    <meta charset="utf-8">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <title>Color Switcher Test - Cigma</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="assets/css/fontawesome.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
</head>

<body>
    <!-- Color Switcher -->
    <div class="color-scheme-wrap active">
        <button class="switchIcon"><i class="fa-solid fa-palette"></i></button>
        <h3 class="color-scheme-wrap-title text-center">Color Switcher</h3>
        <div class="color-switch-btns">
            <button data-color="#FF4F38"><i class="fa-solid fa-droplet"></i></button>
            <button data-color="#3282FB"><i class="fa-solid fa-droplet"></i></button>
            <button data-color="#563BFB"><i class="fa-solid fa-droplet"></i></button>
            <button data-color="#27AE60"><i class="fa-solid fa-droplet"></i></button>
            <button data-color="#FF7E02"><i class="fa-solid fa-droplet"></i></button>
        </div>
    </div>

    <!-- Test Content -->
    <div class="container mt-5 pt-5">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">Color Switcher Test Page</h1>
                <div class="text-center mb-4">
                    <p>Click the color switcher on the right to test the color changing functionality.</p>
                    <p>The selected color should be saved and restored when you refresh the page.</p>
                </div>
                
                <!-- Test Elements with Theme Color -->
                <div class="row gy-4">
                    <div class="col-md-4">
                        <div class="p-4 text-center" style="background-color: var(--theme-color); color: white; border-radius: 8px;">
                            <h4>Theme Color Background</h4>
                            <p>This background uses the theme color</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="p-4 text-center border" style="border-color: var(--theme-color) !important; border-radius: 8px;">
                            <h4 style="color: var(--theme-color);">Theme Color Border & Text</h4>
                            <p>This border and heading use the theme color</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="p-4 text-center" style="border-radius: 8px; background: rgba(255, 79, 56, 0.1);">
                            <button class="btn" style="background-color: var(--theme-color); color: white; border: none; padding: 10px 20px; border-radius: 5px;">
                                Theme Color Button
                            </button>
                            <p class="mt-2">Button with theme color</p>
                        </div>
                    </div>
                </div>
                
                <!-- Instructions -->
                <div class="mt-5 p-4" style="background-color: #f8f9fa; border-radius: 8px;">
                    <h5>Testing Instructions:</h5>
                    <ol>
                        <li>Click on different color buttons in the color switcher</li>
                        <li>Observe how the theme colors change in the elements above</li>
                        <li>Refresh the page to test if the color setting is saved</li>
                        <li>The selected color should be restored after refresh</li>
                        <li>The active color button should have a white dot indicator</li>
                    </ol>
                </div>
                
                <!-- Debug Info -->
                <div class="mt-4 p-3" style="background-color: #e9ecef; border-radius: 8px;">
                    <h6>Debug Information:</h6>
                    <p><strong>Current Theme Color:</strong> <span id="current-color">Loading...</span></p>
                    <p><strong>Saved Color in localStorage:</strong> <span id="saved-color">Loading...</span></p>
                    <button onclick="clearSavedColor()" class="btn btn-sm btn-danger">Clear Saved Color</button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Files -->
    <script src="assets/js/vendor/jquery-3.7.1.min.js"></script>
    <script src="assets/js/bootstrap.min.js"></script>
    <script src="assets/js/main.js"></script>
    
    <script>
        // Debug functions
        function updateDebugInfo() {
            const currentColor = getComputedStyle(document.documentElement).getPropertyValue('--theme-color').trim();
            const savedColor = localStorage.getItem('cigma-theme-color') || 'None';
            
            document.getElementById('current-color').textContent = currentColor;
            document.getElementById('saved-color').textContent = savedColor;
        }
        
        function clearSavedColor() {
            localStorage.removeItem('cigma-theme-color');
            updateDebugInfo();
            location.reload();
        }
        
        // Update debug info on page load and color changes
        $(document).ready(function() {
            updateDebugInfo();
            
            // Monitor for color changes
            $('.color-switch-btns button').on('click', function() {
                setTimeout(updateDebugInfo, 100);
            });
        });
    </script>
</body>
</html>
