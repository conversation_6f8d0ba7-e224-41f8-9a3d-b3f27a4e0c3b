// Primary Button
.th-btn {
    position: relative;
    z-index: 2;
    vertical-align: middle;
    display: inline-flex;
    border: none;
    text-align: center;
    background-color: $theme-color;
    color: $white-color;
    font-family: $body-font;
    font-size: 16px;
    font-weight: 500;
    line-height: 1;
    padding: 24px 48px;
    min-width: 170px;
    border-radius: 50px;
    justify-content: center;
    overflow: hidden;
    transition: 0.5s;
    i {
        transition: 0.1s;
    }
    &:after {
        content: '';
        position: absolute;
        width: 0;
        height: 100%;
        background: $title-color;
        top: 0;
        left: 0;
        z-index: -1;
        transition: 0.7s;
    }
    &:hover,
    &:active {
        background: $theme-color;
        color: $white-color;
        i.fa-arrow-up-right {
            transform: rotate(45deg);
        }
        &:after {
            width: 100%;
        }
    }
    &.style2 {
        background: $smoke-color2;
        color: $title-color;
        &:after {
            background: $theme-color;
        }
        &:hover {
            background: $smoke-color2;
            color: $white-color;
        }
    }
    &.style3 {
        &:after {
            background: $white-color;
        }
        &:hover {
            color: $title-color;
        }
    }
    &.style4 {
        background: $title-color;
        &:after {
            background: $theme-color;
        }
    }
    &.style5 {
        background: $smoke-color2;
        color: $theme-color;
        &:after {
            background: $theme-color;
        }
        &:hover {
            background: $smoke-color2;
            color: $white-color;
        }
    }
    &.style6 {
        background: $theme-color;
        color: $title-color;
        &:after {
            background: $white-color;
        }
        &:hover {
            background: $theme-color;
        }
    }
    &.style7 {
        background: transparent;
        color: $white-color;
        &:after {
            background: $theme-color;
            width: 3px;
        }
        &:hover {
            background: transparent;
            color: $title-color;
            &:after {
                width: 100%;
            }
        }
    }
    &.style8 {
        background: transparent;
        color: $title-color;
        &:after {
            background: $theme-color;
            width: 3px;
        }
        &:hover {
            color: $white-color;
            &:after {
                width: 100%;
            }
        }
    }
    &.style9 {
        background: transparent;
        color: $white-color;
        &:after {
            background: $theme-color;
            width: 3px;
        }
        &:hover {
            background: transparent;
            &:after {
                width: 100%;
            }
        }
    }
    &.style10 {
        background: $title-color;
        &:after {
            background: $theme-color2;
        }
    }
    &.style11 {
        background: $theme-color2;
        &:after {
            background: $title-color;
        }
    }
    &.style12 {
        background: $theme-color2;
        &:after {
            background: $white-color;
        }
        &:hover {
            color: $title-color;
        }
    }
    &.style-border {
        background: transparent;
        border: 1px solid $light-color;
        color: $title-color;
        padding: 23px 48px;
        &:after {
            background: $theme-color;
        }
        &:hover {
            border: 1px solid $theme-color;
            color: $white-color;
        }
    }
    &.style-border2 {
        background: transparent;
        border: 1px solid $white-color;
        color: $white-color;
        &:after {
            background: $theme-color;
        }
        &:hover {
            border-color: $theme-color;
            color: $title-color;
        }
    }
    &.style-border3 {
        background: transparent;
        border: 1px solid $white-color;
        color: $white-color;
        &:after {
            background: $white-color;
        }
        &:hover {
            color: $theme-color;
        }
    }
    &.style-border4 {
        background: transparent;
        border: 1px solid $title-color;
        color: $title-color;
        padding-top: 23px;
        padding-bottom: 23px;
        &:after {
            background: $title-color;
        }
        &:hover {
            color: $white-color;
        }
    }
    &.style-border5 {
        background: transparent;
        border: 1px solid $light-color;
        color: $title-color;
        padding: 23px 48px;
        &:after {
            background: $theme-color;
        }
        &:hover {
            border: 1px solid $theme-color;
            color: $title-color;
        }
    }
    &.btn-fw {
        width: 100%;
        &:before,
        &:after {
            display: none;
        }
        &:hover {
            background: $theme-color;
            color: $white-color;
        }
    }
    &.btn-md {
        padding: 20px 48px;
    }
    &.btn-sm {
        padding: 16px 25px;
        min-width: 140px;
    }
    &.btn-radius-0 {
        border-radius: 0;
    }
    &.btn-radius-8 {
        border-radius: 8px;
    }
    &.circle-btn {
        height: 132px;
        width: 132px;
        min-width: auto;
        border-radius: 50%;
        align-items: center;
        font-weight: 500;
        font-size: 16px;
        padding: 25px;
    }
}
@keyframes greentopBubbles {
    0% {
      background-position: 5% 90%, 10% 90%, 10% 90%, 15% 90%, 25% 90%, 25% 90%,
        40% 90%, 55% 90%, 70% 90%;
    }
  
    50% {
      background-position: 0% 80%, 0% 20%, 10% 40%, 20% 0%, 30% 30%, 22% 50%,
        50% 50%, 65% 20%, 90% 30%;
    }
  
    100% {
      background-position: 0% 70%, 0% 10%, 10% 30%, 20% -10%, 30% 20%, 22% 40%,
        50% 40%, 65% 10%, 90% 20%;
      background-size: 0% 0%, 0% 0%, 0% 0%, 0% 0%, 0% 0%, 0% 0%;
    }
  }
@keyframes greenbottomBubbles {
    0% {
      background-position: 10% -10%, 30% 10%, 55% -10%, 70% -10%, 85% -10%,
        70% -10%, 70% 0%;
    }
  
    50% {
      background-position: 0% 80%, 20% 80%, 45% 60%, 60% 100%, 75% 70%, 95% 60%,
        105% 0%;
    }
  
    100% {
      background-position: 0% 90%, 20% 90%, 45% 70%, 60% 110%, 75% 80%, 95% 70%,
        110% 10%;
      background-size: 0% 0%, 0% 0%, 0% 0%, 0% 0%, 0% 0%, 0% 0%;
    }
}
@keyframes btn-icon-anim {
    0% {
        top: 0;
        right: 2px;
    }
    25% {
        top: -10px;
        right: -10px;
    }
    50% {
        top: 10px;
        opacity: 0;
        right: 17px;
    }
    100% {
        top: 0;
        right: 2px;
        opacity: 1;
    }
}
// Icon Btn
.icon-btn {
    display: inline-block;
    width: var(--btn-size, 56px);
    height: var(--btn-size, 56px);
    line-height: var(--btn-size, 56px);
    font-size: var(--btn-font-size, 20px);
    background-color: var(--icon-bg, $theme-color);
    color: $white-color;
    text-align: center;
    border-radius: 50%;
    border: 0;
    transition: 0.4s ease-in-out;
    position: relative;
    &:hover {
        background-color: $title-color;
        color: $white-color;
        border: 0;
    }
    &.style2 {
        background-color: $theme-color2;
        color: $white-color;
        border: none;
        &:hover {
            background-color: $theme-color;
        }
    }
    &.style3 {
        background-color: $theme-color;
        color: $white-color;
        border: none;
        &:hover {
            background-color: $white-color;
            color: $theme-color;
        }
    }
    &.style4 {
        background-color: $title-color;
        color: $white-color;
        border: none;
        &:hover {
            background-color: $theme-color2;
        }
    }
    &.style-border {
        --icon-bg: transparent;
        border: 1px solid $border-color;
        line-height: 54px;
        color: $title-color;
        &:hover {
            color: $white-color;
        }
    }
}

// Play Button
.play-btn {
    display: inline-block;
    position: relative;
    z-index: 1;

    > i {
        display: inline-block;
        width: var(--icon-size, 56px);
        height: var(--icon-size, 56px);
        line-height: var(--icon-size, 56px);
        text-align: center;
        background-color: $white-color;
        color: $theme-color;
        font-size: var(--icon-font-size, 1.4em);
        border-radius: 50%;
        z-index: 1;
        transition: all ease 0.4s;
    }

    &:after,
    &:before {
        content: "";
        position: absolute;
        left: 0;
        top: 0;
        right: 0;
        bottom: 0;
        background-color: $white-color;
        @extend .ripple-animation;
        z-index: -1;
        border-radius: 50%;
        transition: all ease 0.4s;
    }

    &:after {
        animation-delay: 2s;
    }

    &:hover {
        &:after,
        &::before,
        i {
            background-color: $theme-color;
            color: $white-color;
        }
    }
    &.style2 {
        --icon-size: 80px;
        --icon-font-size: 24px;
        i {
            background-color: $title-color;
        }
        &:hover {
            i {
                background-color: $theme-color;
            }
            &:before,
            &:after {
                background-color: $white-color;
            }
        }
        @include xs {
            --icon-size: 56px;
            --icon-font-size: 20px;
        }
    }
    &.style3 {
        --icon-size: 56px;
        --icon-font-size: 16px;
        > i {
            background-color: $theme-color2;
            color: $white-color;
        }
        &:before,
        &:after {
            background-color: $theme-color2;
        }
        &:hover {
            > i {
                background-color: $theme-color;
                color: $white-color;
            }
            &:before,
            &:after {
                background-color: $theme-color;
            }
        }
    }
    &.style4 {
        > i {
            --icon-size: 100px;
            color: $title-color;
        }
        &:before,
        &:after {
            background-color: transparent;
            border: 1px solid $title-color;
        }
        &:hover {
            > i {
                color: $white-color;
            }
            &:before,
            &:after {
                border: 1px solid $white-color;
            }
        }
    }
    &.style5 {
        --icon-size: 56px;
        --icon-font-size: 16px;
        > i {
            background-color: $white-color;
            color: $theme-color2;
        }
        &:before,
        &:after {
            background-color: $theme-color2;
        }
        &:hover {
            > i {
                background-color: $theme-color;
                color: $white-color;
            }
            &:before,
            &:after {
                background-color: $theme-color;
            }
        }
    }
    &.style6 {
        > i {
            --icon-size: 100px;
            color: $white-color;
            font-size: 20px;
            background: transparent;
            border: 2px solid $white-color;
        }
        &:before,
        &:after {
            background: transparent;
            border: 1px solid $white-color;
        }
        &:hover {
            > i {
                color: $theme-color;
                border-color: $theme-color;
            }
            &:before,
            &:after {
                border-color: $theme-color;
            }
        }
        @include xs {
            > i {
                --icon-size: 60px;
            }
        }
    }
    &.style7 {
        > i {
            --icon-size: 80px;
            background: $theme-color2;
            color: $white-color;
        }
        &:before,
        &:after {
            background-color: $theme-color2;
        }
        &:hover {
            > i {
                background: $white-color;
                color: $theme-color2;
            }
            &:before,
            &:after {
                background: $white-color;
            }
        }
        @include sm {
            > i {
                --icon-size: 60px;
            }
        }
    }
    &.style8 {
        > i {
            --icon-size: 40px;
            --icon-font-size: 16px;
            background: $white-color;
            color: $title-color;
        }
    }
    &.style9 {
        > i {
            --icon-size: 80px;
            --icon-font-size: 20px;
            background: $title-color;
            color: $theme-color;
        }
        &:before,
        &:after {
            border: 1px solid $white-color;
        }
        &:hover {
            > i {
                background: $theme-color2;
                color: $white-color;
            }
        }
    }
}

// Link Button
.link-btn {
    font-weight: 600;
    font-size: 14px;
    display: inline-flex;
    line-height: 0.8;
    position: relative;
    padding-bottom: 2px;
    margin-bottom: -2px;
    text-transform: uppercase;
    color: $title-color;
    .icon {
        overflow: hidden;
        display: inline-block;
        position: relative;
        top: -2px;
    }
    i {
        transition: 0.1s all;
        position: relative;
        font-size: 14px;
        top: -1px;
    }

    &:before {
        content: "";
        position: absolute;
        left: 0;
        bottom: 0;
        width: 0;
        height: 1px;
        background-color: $theme-color;
        transition: all ease 0.4s;
    }
    &:hover,
    &.active {
        color: $theme-color;
        &::before {
            width: 100%;
        }
        i.fa-arrow-up-right {
            transform: rotate(45deg);
        }
    }
    &.style2 {
        color: $white-color;
        &:before {
            width: 100%;
            background: $white-color;
        }
        &:hover {
            color: $theme-color;
            &:before {
                background: $theme-color;
                width: 70%;
            }
        }
    }
    &.style3 {
        color: $theme-color;
        margin-bottom: 0;
        &:before {
            width: 100%;
            background: $theme-color;
        }
        &:hover {
            color: $white-color;
            &:before {
                background: $white-color;
                width: 70%;
            }
        }
    }
    &.style4 {
        color: $title-color;
        margin-bottom: 0;
        &:before {
            width: 100%;
            background: $title-color;
        }
        &:hover {
            &:before {
                width: 70%;
            }
        }
    }
}
// Scroll To Top
.scroll-top {
    position: fixed;
    right: 30px;
    bottom: 55px;
    height: 50px;
    width: 50px;
    cursor: pointer;
    display: block;
    border-radius: 50px;
    z-index: 10000;
    opacity: 1;
    visibility: hidden;
    transform: translateY(45px);
    transition: all 300ms linear;
    &:after {
        content: "\f102";
        font-family: $icon-font;
        position: absolute;
        text-align: center;
        line-height: 50px;
        font-size: 20px;
        color: $theme-color;
        left: 0;
        top: 0;
        height: 50px;
        width: 50px;
        cursor: pointer;
        display: block;
        z-index: 1;
        border: 2px solid $theme-color;
        box-shadow: none;
        border-radius: 50%;
    }
    svg {
        color: $theme-color;
        border-radius: 50%;
        background: $white-color;
        path {
            fill: none;
        }
    }
    .progress-circle path {
        stroke: $theme-color;
        stroke-width: 20px;
        box-sizing: border-box;
        transition: all 400ms linear;
    }
    &.show {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
    }
    &.style2 {
        svg {
            background: $black-color2;
        }
    }
}
