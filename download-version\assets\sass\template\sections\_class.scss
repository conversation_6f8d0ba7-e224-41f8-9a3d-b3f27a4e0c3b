/* Class area 1 ---------------------------------- */
.class-card {
    border-radius: 30px;
    background: $black-color2;
    padding: 30px 30px 0;
    text-align: center;
    position: relative;
    z-index: 1;
    margin-bottom: 28px;
    &:before,
    &:after {
        content: '';
        position: absolute;
        inset: 0;
        border-radius: 30px;
        background: linear-gradient(transparent, $border-color5);
        z-index: -2;
        transition: 0.4s;
    }
    &:before {
        background: $black-color2;
        inset: 1px;
        z-index: -1;
    }
    &_img {
        border-radius: 15px;
        overflow: hidden;
        margin-bottom: 30px;
        img {
            width: 100%;
            transition: 0.4s;
        }
    }
    .box-title {
        font-weight: 500;
        margin-bottom: 15px;
        a {
            color: $white-color;
            &:hover {
                color: $theme-color;
            }
        }
    }
    .class-card_text {
        color: $light-color;
    }
    .btn-wrap {
        justify-content: center;
        transform: translate(0, 50%);
        margin-top: -24px;
    }
    .icon-btn {
        border-radius: 50%;
        background: $border-color5;
        border-color: $border-color5;
        color: $white-color;
        position: relative;
        &:after {
            content: '';
            position: absolute;
            inset: -10px -10px 50%;
            background: $black-color2;
            z-index: -1;
            border-radius: 50% 50% 0 0/100% 100% 0 0;
            border: 1px solid $border-color5;
            border-bottom: 0;
        }
        &:hover {
            background: $theme-color;
            border-color: $theme-color;
        }
    }
    &:hover {
        &:after {
            background: linear-gradient(transparent, $theme-color);
        }
        .class-card_img {
            img {
                transform: scale(1.05);
            }
        }
        .icon-btn {
            &:after {
                border-color: $theme-color;
            }
        }
    }
}

/* Class area 2 ---------------------------------- */
.class-area-2 {
    padding-bottom: 30px;
}
.class-bg-shape {
    position: absolute;
    inset: 0;
}
.class-slider2 {
    padding: 0 130px;
    @include xs {
        padding: 0;
        margin: 0 15px;
    }
}
.class-card2 {
    position: relative;
    .class-card_img {
        border-radius: 30px;
        margin-bottom: 0;
    }
    .box-title {
        margin-bottom: 13px;
        font-weight: 500;
        a {
            color: $white-color;
            &:hover {
                color: $theme-color;
            }
        }
    }
    .class-card_text {
        color: $light-color;
        margin-bottom: 22px;
    }
    .class-card_content {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        border-radius: 30px;
        background: rgba(29, 34, 41, 0.7);
        border: 1px solid $border-color5;
        backdrop-filter: blur(5.1px);
        margin: 0 10px;
        text-align: center;
        padding: 30px;
        transition: 0.4s;
        .btn-wrap {
            justify-content: center;
        }
        .class-card_text {
            margin-bottom: -0.4em;
        }
    }
    .class-card_hover-content {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        border-radius: 30px;
        background: rgba(29, 34, 41, 0.7);
        border: 1px solid $border-color5;
        backdrop-filter: blur(5.1px);
        margin: 0 10px;
        text-align: center;
        padding: 30px;
        transition: 0.4s;
        opacity: 0;
        visibility: hidden;
        transform: scaleY(0);
        .btn-wrap {
            justify-content: center;
        }
    }
    &:hover {
        .class-card_content {
            opacity: 0;
            visibility: hidden;
        }
        .class-card_hover-content {
            opacity: 1;
            visibility: visible;
            transform: scaleY(1);

        }
    }
}

