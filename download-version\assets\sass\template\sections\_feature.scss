
/* Feature card ---------------------------------- */
.feature-area-1 {
    position: relative;
    padding-top: 60px;
    margin-top: -173px;
    z-index: 2;
    .feature-bg-wrap {
        position: absolute;
        inset: 0;
    }
    .feature-bg-shape1-1 {
        position: absolute;
        width: 788px;
        height: 100%;
        bottom: 0;
        left: 0;
        border-top: solid 285px $theme-color;
        border-left: solid 394px $theme-color;
        border-right: solid 394px transparent;
        border-bottom: solid 285px transparent;
    }
    @include hd {
        margin-top: -200px;
    }
    @include xxl {
        padding-top: 70px;
        margin-top: -144px;
    }
    @include ml {
        margin-top: -130px;
        .feature-bg-shape1-1 {
            width: 600px;
            border-top: solid 285px var(--theme-color);
            border-left: solid 300px var(--theme-color);
            border-right: solid 300px transparent;
            border-bottom: solid 285px transparent;
        }
    }
    @include xl {
        margin-top: -115px;
    }
    @include lg {
        border-radius: 100px 100px 0 0;
        padding-top: 120px;
        .feature-bg-wrap {
            mask-image: none !important;
        }
        .feature-bg-shape1-1 {
            display: none;
        }
    }
    @include md {
        margin-top: 0;
        border-radius: 0;
        padding-top: 80px;
    }
}
.feature-card {
    padding: 30px;
    position: relative;
    box-shadow: 0px 10px 30px -6px rgba(12, 12, 13, 0.12);
    border-radius: 100px;
    background: $white-color;
    z-index: 1;
    overflow: hidden;
    text-align: center;
    transition: 0.4s;
    .feature-card-bg-shape {
        position: absolute;
        left: 50%;
        bottom: 22px;
        transform: translate(-50%, 0);
        z-index: -1;
        transition: 0.4s;
        width: max-content;
    }
    .box-icon {
        display: inline-block;
        margin-bottom: 30px;
        transition: 0.4s;
    }
    .box-title {
        font-size: 24px;
        font-weight: 700;
        transition: 0.4s;
        margin-bottom: 6px;
    }
    .box-text {
        margin-bottom: 11px;
        transition: 0.4s;
    }
    .link-btn {
        color: $title-color;
        font-size: 14px;
        font-weight: 700;
        text-transform: capitalize;
        &:before {
            background: $title-color;
        }
    }
    &:hover {
        .box-icon {
            img {
                transform: rotateY(180deg);
            }
        }
    }
    @include xl {
        border-radius: 50px;
    }
    @include lg {
        .box-title {
            font-size: 24px;
        }
    }
}
