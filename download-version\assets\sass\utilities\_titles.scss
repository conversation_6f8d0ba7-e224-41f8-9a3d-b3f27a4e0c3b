.sec-title {
    margin-bottom: calc(var(--section-title-space) - 11px);
    margin-top: -0.24em;
    font-weight: 700;
    &.style2 {
        font-size: 40px;
        @include ml {
            font-size: 36px;
        }
        @include sm {
            font-size: 34px;
        }
        @include sm {
            font-size: 28px;
        }
    }
}

.sub-title {
    display: inline-block;
    align-items: center;
    font-size: 16px;
    font-weight: 600;    
    color: $theme-color;
    text-transform: uppercase;
    margin-bottom: 32px;
    line-height: 24px;
    position: relative;
    padding-bottom: 4px;
    &:before,
    &:after {
        content: '';
        position: absolute;
        height: 2px;
        width: 100%;
        left: 0;
        bottom: 0;
        background: $title-color;
        display: inline-block;
    }
    &:before {
        width: 20px;
        background: $theme-color;
        z-index: 1;
        animation: movingY 8s linear infinite;
    }
    &.text-white {
        &:after {
            background: $white-color;
        }
    }
    &.after-none {
        &:after {
            display: none;
        }
    }
    &.before-none {
        &:before {
            display: none;
        }
    }
    &.h4 {
        font-size: 30px;
        font-weight: 600;
        line-height: 1;
        margin-top: -0.14em;
    }
    &.after-lg-none {
        @media (min-width: 992px) {
            &:after {
                display: none;
            }
        }
    } 
    &.style2 {
        font-weight: 500;
        display: inline-flex;
        gap: 8px;
        padding-bottom: 0;
        line-height: 1;
        &:after,
        &:before {
            position: relative;
            height: 12px;
            width: 4px;
            background: $theme-color;
            animation: none;
        }
    }  
    &.style3 {
        font-size: 16px;
        font-weight: 400;
        font-family: $body-font;
        background: transparent;
        padding: 10px 16px;
        &:before {
            display: none;
        }
        &:after {
            width: auto;
            height: auto;
            inset: 0;
            border-radius: 48px;
            border: 1px solid transparent;
            background: linear-gradient(0, transparent 0%, $theme-color 100%) border-box;
            -webkit-mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
            -webkit-mask-composite: destination-out;
            mask-composite: exclude;
        }
    }
    &.style4 {
        font-weight: 500;
        color: $title-color;
        padding-bottom: 2px;
        margin-bottom: 24px;
        &:after {
            display: none;
        }
        &:before {
            width: 100%;
            height: 12px;
            z-index: -1;
            animation: none;
        }
    }
}
.sec-text {
    font-size: 18px;
}
.box-title {
    font-size: 24px;
    line-height: 1.417;
    font-weight: 600;
    margin-top: -0.32em;
    a {
        color: inherit;
        &:hover {
            color: $theme-color;
        }
    }
}
.box-subtitle {
    margin-bottom: 8px;
}

.box-title2 {
    font-size: 20px;
    line-height: 1.5;
    font-weight: 700;
    margin-top: -0.35em;
    a {
        color: inherit;
        &:hover {
            color: $theme-color;
        }
    }
}
.title-area {
    margin-bottom: calc(var(--section-title-space) - 12px);
    position: relative;
    z-index: 2;
    &:has(.sub-title) {
        margin-top: -0.4em;
    }
    .sec-title {
        margin-bottom: 18px;
    }
    &.mb-0 {
        .sec-title {
            margin-bottom: -0.24em;
        }
    }
    &.text-center {
        .sub-title {
            justify-content: center;
        }
    }
    &.text-lg-start.text-center {
        @media (min-width: 992px) {
            .sub-title {
                justify-content: start;
            }
        }
    }
}
.mb-32 {
    margin-bottom: 32px;
}

hr.title-line {
    margin-top: 0;
    background-color: $border-color;
    opacity: 1;
    margin-bottom: var(--section-title-space) !important;
}
.sec-btn {
    text-align: center;
}
.sec-btn,
.title-line {
    margin-bottom: var(--section-title-space);
}

.shadow-title {
    font-family: $title-font;
    font-size: 74px;
    font-weight: 900;
    line-height: 1;
    background-image: linear-gradient(180deg, rgba(226, 232, 250, 0.7) 0%, rgba(226, 232, 250, 0) 88.54%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
    margin: -0.55em 0 -0.45em -0.25em;
    &.color2 {
        background-image: linear-gradient(180deg, #232C47 0%, rgba(20, 29, 56, 0) 91.15%);
    }
    &.color3 {
        background-image: linear-gradient(180deg, #E0E0E0 0%, rgba(220, 214, 214, 0) 93.75%);
    }
}

.title-area2 {
    padding: 50px 100px;
    .subtitle {
        color: $white-color;
        text-transform: uppercase;
        margin-top: -0.4em;
        margin-bottom: 5px;
        display: block;
    }
    .title {
        color: $white-color;
        max-width: 430px;
        margin-bottom: -0.26em;
    }
}
.page-subtitle {
    font-size: 16px;
    font-weight: 500;
    font-family: $body-font;
    display: inline-block;
    position: relative;
    &:after,
    &:before {
        content: '';
        position: relative;
        width: 4px;
        height: 12px;
        background: $theme-color;
        display: inline-block;
        margin: 0 8px;
    }
    &:before {
        margin-left: 0;
    }
    &:after {
        margin-right: 0;
    }
}
.page-title {
    font-size: 40px;
    font-weight: 600;
    @include sm {
        font-size: 36px;
    }
    @include xs {
        font-size: 32px;
    }
}
@media (max-width: 1700px) {
    .title-area2 {
        padding: 50px 50px;
    }
}


@include lg {
    .shadow-title {
        font-size: 64px;
    }
    .title-area,
    .sec-title {
        --section-title-space: 60px;
        &.mb-45 {
            margin-bottom: 36px;
        }
        &.mb-50 {
            margin-bottom: 40px;
        }
    }
    .sec-btn,
    .title-line {
        --section-title-space: 55px;
    }
    .title-area2 .title {
        max-width: 300px;
    }
}

@include md {
    .shadow-title {
        font-size: 60px;
    }
    .title-area,
    .sec-title {
        --section-title-space: 50px;
        &.mb-45 {
            margin-bottom: 35px;
        }
    }
    .sec-btn,
    .title-line {
        --section-title-space: 50px;
    }
    .sub-title {
        &.h4 {
            font-size: 22px;
        }
    }
}

@include sm {
    .title-area2 {
        text-align: center;
        .title {
            max-width: 100%;
        }
    }
}

@include xs {
    .shadow-title {
        font-size: 52px;
    }
}

@include vxs {
    .shadow-title {
        font-size: 40px;
    }
    .title-area2 {
        padding: 40px 20px;
    }
}