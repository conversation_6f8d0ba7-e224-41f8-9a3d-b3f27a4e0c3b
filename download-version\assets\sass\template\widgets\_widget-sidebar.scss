.widget {
    padding: var(--widget-padding-y, 32px) var(--widget-padding-x, 24px);
    background-color: transparent;
    margin-bottom: 40px;
    position: relative;
    border-radius: 8px;
    border: 1px solid $border-color;

    &[data-overlay]:before {
        z-index: -1;
        border-radius: 8px;
    }
    select,
    input,
    .form-select,
    .form-control,
    textarea {
        background-color: $white-color;
    }
    textarea {
        min-height: 120px;
    }
    .form-group {
        > i {
            background-color: $white-color;
        }
    }
}
.wp-block-archives__label,
.widget_title {
    position: relative;
    font-size: 24px;
    font-weight: 600;
    font-family: $title-font;
    line-height: 1em;
    margin: -0.12em 0 32px 0;
    padding-bottom: 20px;
    border-bottom: 3px solid $border-color2;
    &:after {
        content: '';
        position: absolute;
        bottom: -3px;
        left: 0;
        height: 3px;
        width: 50px;
        background: $theme-color;
    }
}
.widget-form {
    --bs-gutter-x: 20px;
}

.widget {
    .search-form {
        position: relative;
        display: flex;
        input {
            flex: 1;
            border-radius: 100px;
            padding-right: 85px;
            border: 1px solid $border-color;
            height: 56px;
            &:focus {
                border-color: $theme-color;
            }
        }
        button {
            border: none;
            font-size: 16px;
            background-color: $theme-color;
            color: $white-color;
            display: inline-block;
            padding: 0;
            width: 40px;
            height: 40px;
            text-align: center;
            border-radius: 50%;
            position: absolute;
            top: 8px;
            right: 8px;
            &:hover {
                background-color: $title-color;
            }
        }
    }
}

.wp-block-tag-cloud,
.tagcloud {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    a {
        display: inline-block;
        font-size: 16px;
        font-weight: 400;
        font-family: $body-font;
        text-transform: capitalize;
        line-height: 1;
        padding: 10px 11px;
        color: $title-color;
        background-color: $smoke-color2;
        border-radius: 4px;
        &:hover {
            background-color: $theme-color;
            color: $white-color !important;
            border-color: $theme-color;
        }
    }
}

.sidebar-gallery {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    max-width: 320px;
    .gallery-btn {
        position: absolute;
        top: 20px;
        left: 0;
        color: $white-color;
        visibility: hidden;
        opacity: 0;
        height: 100%;
        width: 100%;
        transform: none;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        &:hover {
            color: $theme-color;
        }
    }
    .gallery-thumb {
        overflow: hidden;
        position: relative;
        border-radius: 20px;
        &:before {
            content: '';
            height: 100%;
            width: 100%;
            background-color: $theme-color;
            mix-blend-mode: multiply;
            opacity: 0.8;
            position: absolute;
            top: 0;
            left: 0;
            transform: scaleX(0);
            border-radius: inherit;
            transition: 0.4s ease-in-out;
        }
        img {
            width: 100%;
        }
        &:hover {
            &:before {
                transform: scaleX(1);
            }
            .gallery-btn {
                visibility: visible;
                opacity: 1;
                top: 0;
                &:hover {
                    color: $white-color;
                }
            }
        }
    }
    .wp-block-image {
        overflow: hidden;
        position: relative;
        border-radius: 5px;
        width: 100% !important;
        img {
            width: 100%;
        }
    }
}

.recent-post {
    display: flex;
    align-items: center;
    margin-bottom: 32px;

    &:last-child {
        margin-bottom: 0;
    }

    .media-img {
        margin-right: 22px;
        width: 85px;
        overflow: hidden;
        border-radius: 4px;
        position: relative;
        img {
            width: 100%;
            transition: 0.4s ease-in-out;
        }
        &:after {
            content: '\f0c1';
            font-family: $icon-font;
            position: absolute;
            inset: 0;
            text-align: center;
            line-height: 85px;
            font-size: 24px;
            background-color: rgba(0, 0, 0, 0.7);
            color: $theme-color;
            pointer-events: none;
            transform: scale(0);
            transition: 0.4s ease-in-out;
        }
    }

    .post-title {
        font-weight: 600;
        font-size: 18px;
        line-height: 26px;
        margin: -0.35em 0 10px 0;
        font-family: $title-font;
        text-transform: capitalize;
    }

    .recent-post-meta {
        margin-top: -0.3em;
        a {
            text-transform: capitalize;
            font-size: 14px;
            color: $body-color;
            font-family: $body-font;

            i {
                margin-right: 8px;
                color: $theme-color;
            }
            &:hover {
                color: $theme-color;
            }
        }
    }
    &:hover {
        .media-img {
            img {
                transform: scale(1.1);
            }
            &:after {
                transform: scale(1);
            }
        }
    }
}

.sidebar-area {
    &.sidebar-sticky {
        position: sticky;
        top: 140px;
        @include md {
            position: relative;
            top: auto;
            margin-bottom: -40px;
        }
    }
    ul.wp-block-latest-posts {
        margin-bottom: 0;

        li {
            &:last-child {
                margin-bottom: 0;
            }
        }
    }
    select,
    input {
        background-color: $white-color;
        border-radius: 50px;
    }
    .sidebar-gallery {
        margin-bottom: 0 !important;
    }

    .widget .wp-block-search {
        margin-bottom: 0;
    }

    .wp-block-search__label,
    .wp-block-group__inner-container h2 {
        position: relative;
        font-size: 24px;
        font-weight: 700;
        font-family: $title-font;
        line-height: 1em;
        padding-bottom: 20px;
        margin: -0.12em 0 38px 0;
        &:after,
        &:before {
            content: '';
            height: 3px;
            width: 100%;
            background-color: $border-color;
            position: absolute;
            bottom: 0;
            left: 0;
        }
        &:after {
            background-color: $theme-color;
            width: 40px;
        }
    }

    ol.wp-block-latest-comments {
        padding: 0;
        margin: 0;

        li {
            line-height: 1.5;
            margin: 0 0 20px 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            padding-bottom: 20px;

            &:last-child {
                margin-bottom: 0;
                padding-bottom: 0;
                border-bottom: none;
            }
        }
    }
}

// Download Widget --------------------
.download-widget-wrap {
    .th-btn {
        width: 100%;
        &:first-child {
            margin-bottom: 15px;
        }
        &.style2 {
            color: $body-color;
            &:before {
                background-color: $theme-color;
            }
            &:hover {
                color: $white-color;
                border-color: $theme-color;
            }
        }
    }
}

// Widget Info ---------------------
.widget_info {
    border: 0;
    background: $smoke-color;
    padding: 0 !important;
    border-radius: 16px;
    overflow: hidden;
    .th-btn {
        width: 100%;
        margin-bottom: 10px;
        display: block;
    }
    .th-video {
        margin-bottom: 20px;
    }
    .widget_title {
        text-align: center;
        font-size: 28px;
        font-weight: 600;
        color: $white-color;
        background: $theme-color;
        border: 0;
        padding: 36px 20px;
        margin: 0;
        &:after {
            display: none;
        }
    }
}
.info-list {
    margin-top: -20px;
    padding: 48px 48px 48px;
    ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }
    strong {
        font-weight: 600;
        color: $title-color;
        display: block;
        font-size: 18px;
        position: relative;
        margin-bottom: 4px;
        &:after {
            content: '';
            position: absolute;
            width: 2px;
            height: 13px;
            top: 6px;
            left: -10px;
            background: $theme-color;
        }
    }
    li {
        border-bottom: 1px solid $border-color;
        padding: 16px 0;
        display: flex;
        align-items: center;
        gap: 5px;
        padding-left: 10px;
        &:last-child {
            border-bottom: none;
            padding-bottom: 0;
            margin-bottom: 0;
        }
        a {
            color: $body-color;
        }
        .th-social {
            margin-top: 10px;
        }
    }
    @include xxl {
        padding: 38px 30px 38px;
    }
}

// Price Filter --------------
.price_slider_wrapper {
	margin-top: -0.5em;
    .price_label {
        span {
            display: inline-block;
            color: $body-color;
        }
    }

    .ui-slider {
        height: 6px;
        position: relative;
        width: 100%;
        background-color: $theme-color;
        border: none;
        margin-top: 15px;
        margin-bottom: 25px;
        cursor: pointer;
        border-radius: 0;
    }

    .ui-slider-range {
        border: none;
        cursor: pointer;
        position: absolute;
        top: 0;
        height: 100%;
        z-index: 1;
        display: block;
        background-color: $title-color;
    }

    .ui-slider-handle {
        width: 16px;
        height: 16px;
        line-height: 16px;
        border-radius: 50%;
        text-align: center;
        padding: 0;
        cursor: pointer;
        position: absolute;
        margin-top: -5px;
        z-index: 2;
        background-color: $white-color;
        border: 3px solid $title-color;
        transform: translateX(-1px);

        &:focus {
            outline: none;
            box-shadow: 1.5px 2.598px 10px 0px rgba(0, 0, 0, 0.15);
        }

        // &:before {
        //     content: "";
        //     position: absolute;
        //     background-color: #fff;
        //     top: 50%;
        //     left: 50%;
        //     width: 12px;
        //     height: 12px;
        //     border-radius: 50%;
        //     transform: translate(-50%, -50%);
        // }
        &:last-child {
            transform: translateX(-9px);
        }
    }
    button,
    .button {
        background-color: $theme-color;
        color: $white-color;
        font-weight: 500;
        line-height: 1.6;
        text-transform: capitalize;
        text-align: center;
        border: none;
        display: inline-block;
        overflow: hidden;
        position: relative;
        z-index: 2;
        padding: 7px 20px;
        min-width: 100px;
        font-size: 14px;
        border-radius: 0;
        transition: 0.4s ease-in;
        &:hover {
            background-color: $title-color;
        }
    }
}

.widget_shopping_cart {
    .widget_title {
        margin-bottom: 35px;
        border-bottom: none;
    }
    ul {
        margin: 0;
        padding: 0;

        li {
            list-style-type: none;
        }
    }

    .mini_cart_item {
        position: relative;
        padding: 30px 30px 30px 90px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        margin-bottom: 0;
        text-align: left;

        &:first-child {
            border-top: 1px solid rgba(0, 0, 0, 0.1);
        }
    }

    .cart_list {
        a:not(.remove) {
            display: block;
            color: $body-color;
            font-size: 16px;
            font-weight: 500;
            font-family: $title-font;
            font-weight: 600;
            color: $title-color;

            &:hover {
                color: $theme-color;
            }
        }

        a.remove {
            position: absolute;
            top: 50%;
            left: 95%;
            transform: translateY(-50%);
            color: $body-color;

            &:hover {
                color: $theme-color;
            }
        }

        img {
            width: 75px;
            height: 75px;
            position: absolute;
            left: 0;
            top: 18px;
        }
    }

    .quantity {
        display: inline-flex;
        white-space: nowrap;
        vertical-align: top;
        margin-right: 20px;
        font-size: 14px;
        font-weight: 500;
    }

    .total {
        margin-top: 20px;
        font-size: 18px;
        color: $title-color;
        font-family: $body-font;
        strong {
            font-family: $title-font;
        }
    }

    .amount {
        padding-left: 5px;
    }

    .th-btn {
        margin-right: 15px;
        &:last-child {
            margin-right: 0;
        }
    }
}
.widget_banner {
    --widget-padding-y: 68px;
    .title {
        font-size: 24px;
        font-weight: 600;
        line-height: 1.333;
        color: $white-color;
        margin-bottom: 24px;
        margin-top: -0.3em;
    }
    .logo {
        background: $white-color;
        padding: 13px 21px 13px 9px;
        display: inline-block;
        border-radius: 48px;
        margin-bottom: 25px;
    }
    .subtitle {
        color: $white-color;
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 6px;
    }
    .link {
        font-size: 18px;
        font-weight: 500;
        font-family: $title-font;
        color: $white-color;
        margin-bottom: 15px;
        display: block;
        &:hover {
            color: $theme-color;
        }
    }
    .th-btn {
        padding: 20px 48px;
    }
    @include lg {
        .subtitle {
            font-size: 20px;
            line-height: normal;
        }
        .title {
            font-size: 20px;
            line-height: 1.5;
        }
    }
}
.th-video-widget {
    .video-thumb {
        position: relative;
    }

    .play-btn {
        --icon-size: 60px;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);

        i {
            font-size: 16px;
        }
    }

    .video-thumb-title {
        font-size: 20px;
        font-weight: 600;
        line-height: 1.4;
        margin-top: 10px;
        margin-bottom: -0.3em;
    }
}

.widget_recent_entries {
    ul {
        margin: -0.3em 0 0 0;
        padding: 0;
        list-style: none;

        li {
            > a {
                color: $body-color;
                font-weight: 500;
                display: inline-block;

                &:hover {
                    color: $theme-color;
                }
            }

            span.post-date {
                font-size: 14px;
            }

            &:not(:last-child) {
                border-bottom: 1px solid #dadada;
                padding-bottom: 12px;
                margin-bottom: 12px;
            }
        }
    }
}
.widget_recent_comments,
.wp-block-latest-comments {
    ul {
        list-style-type: none;
        padding-left: 0;
    }
}
ul.widget_recent_comments,
ol.widget_recent_comments,
.wp-block-latest-comments {
    margin-top: -0.11em;
    padding-left: 0;
}

.widget_recent_comments,
.wp-block-latest-comments {
    ol,
    ul {
        margin-bottom: 0;
    }
    li {
        margin-bottom: 0;
        color: $body-color;
        padding-left: 30px;
        position: relative;
        &:before {
            content: "\f086";
            position: absolute;
            left: 0;
            top: -1px;
            color: var(--theme-color);
            font-family: var(--icon-font);
        }
    }
    &.has-avatars {
        li {
            padding-left: 0;
            padding-bottom: 0 !important;
            &:before {
                display: none;
            }
        }
    }
    .avatar {
        margin-top: 0.4em;
    }
    li:not(:last-child) {
        padding-bottom: 12px;
    }
    article {
        line-height: 1.5;
    }
    a {
        color: inherit;
        &:hover {
            color: $theme-color;
        }
    }
}
.wp-block-latest-comments__comment {
    line-height: 1.6;
    a {
        color: $body-color;
        &:hover {
            color: $theme-color;
        }
    }
    &:last-child {
        margin-bottom: 0;
    }
}

.wp-block-calendar tbody td,
.wp-block-calendar th {
    padding: 10px;
}

.wp-block-calendar,
.calendar_wrap {
    position: relative;
    background-color: #fff;
    padding-bottom: 0;
    border: none;

    span[class*="wp-calendar-nav"] {
        position: absolute;
        top: 9px;
        left: 20px;
        font-size: 14px;
        color: $white-color;
        font-weight: 400;
        z-index: 1;
        line-height: 1.7;

        a {
            color: inherit;
        }
    }

    span.wp-calendar-nav-next {
        left: auto;
        right: 20px;
    }

    caption {
        caption-side: top;
        text-align: center;
        color: $white-color;
        background-color: $theme-color;
    }

    th {
        font-size: 14px;
        padding: 5px 5px;
        border: none;
        text-align: center;
        border-right: 1px solid #fff;
        color: $title-color;
        font-weight: 500;
        &:first-child {
            border-left: 1px solid #eee;
        }
        &:last-child {
            border-right: 1px solid #eee;
        }
    }
    table {
        th {
            font-weight: 500;
        }
    }

    td {
        font-size: 14px;
        padding: 5px 5px;
        color: #01133c;
        border: 1px solid #eee;
        text-align: center;
        background-color: transparent;
        transition: all ease 0.4s;
    }

    #today {
        color: $theme-color;
        background-color: $white-color;
        border-color: #ededed;
    }

    thead {
        background-color: #fff;
    }

    .wp-calendar-table {
        margin-bottom: 0;
    }

    .wp-calendar-nav {
        .pad {
            display: none;
        }
    }

    a {
        color: inherit;
        text-decoration: none;
        &:hover {
            color: $title-color;
        }
    }
}

.wp-block-calendar {
    border: none;
    padding-bottom: 0;

    table {
        caption {
            color: $white-color;
        }
    }
}
ul,
ol {
    &.widget_rss,
    &.wp-block-rss {
        padding-left: 0;
    }
}
.widget_rss,
.wp-block-rss {
    list-style-type: none;
    ul {
        margin: -0.2em 0 -0.5em 0;
        padding: 0;
        list-style: none;

        .rsswidget {
            color: var(--title-color);
            font-family: var(--theme-font);
            font-size: 18px;
            display: block;
            margin-bottom: 10px;
        }

        .rssSummary {
            font-size: 14px;
            margin-bottom: 7px;
            line-height: 1.5;
        }

        a {
            display: block;
            font-weight: 600;
            color: inherit;

            &:hover {
                color: $theme-color;
            }
        }

        .rss-date {
            font-size: 14px;
            display: inline-block;
            margin-bottom: 5px;
            font-weight: 400;
            color: $title-color;

            &:before {
                content: "\f073";
                font-family: $icon-font;
                margin-right: 10px;
                font-weight: 300;
                color: $theme-color;
            }
        }

        cite {
            font-weight: 500;
            color: $title-color;
            font-family: $body-font;
            font-size: 14px;

            &:before {
                content: "";
                position: relative;
                top: -1px;
                left: 0;
                width: 20px;
                height: 2px;
                display: inline-block;
                vertical-align: middle;
                margin-right: 8px;
                background-color: $theme-color;
            }
        }
    }

    li:not(:last-child) {
        margin-bottom: 16px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        padding-bottom: 16px;
    }
    a {
        &:hover {
            color: $theme-color;
        }
    }
}

.textwidget {
    margin-top: -0.1em;
}

.sidebar-area {
    .widget_shopping_cart .th-btn {
        margin-right: 10px;
        padding: 8px 22px;
        font-size: 14px;
    }
}
.widget:has(.author-widget-wrap) {
    padding: 0;
    overflow: hidden;
}
.author-widget-wrap {
    text-align: center;
    position: relative;
    padding: var(--widget-padding-y, 40px) var(--widget-padding-x, 40px);
    .author-tag {
        background: $theme-color;
        position: absolute;
        left: 120px;
        top: -40px;
        font-size: 24px;
        font-weight: 800;
        color: $white-color;
        transform: rotate(-45deg) translate(-100%, 0);
        transform-origin: top left;
        padding: 15px 57px;
    }
    .avater {
        display: inline-block;
        img {
            border-radius: 50%;
            width: 120px;
            height: 120px;
        }
    }
    .name {
        font-size: 30px;
        font-weight: 700;
        margin-top: 22px;
        margin-bottom: 10px;
    }
    .meta {
        display: block;
        margin-bottom: 4px;
        a {
            color: $body-color;
            &:hover {
                color: $theme-color;
            }
        }
        &:last-child {
            margin-bottom: -0.3em;
        }
        i {
            color: $theme-color2;
            margin-right: 6px;
        }
    }
    @include lg {
        .name {
            font-size: 22px;
        }
        .author-tag {
            font-size: 18px;
            left: 100px;
        }
    }
}
.widget_schedule {
    background: $smoke-color;
    border-radius: 8px;
    border: 0;
    .widget_title {
        padding-bottom: 0;
        border-bottom: 0;
        margin-bottom: 20px;
        &:after {
            display: none;
        }
    }
    .checklist {
        margin-top: 38px;
        margin-bottom: 20px;
    }
    .th-btn {
        margin-top: 27px;
    }
}
.widget_banner2 {
    .widget-banner {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 8px;
        padding: 24px 0;
        .icon-btn {
            --btn-size: 64px;
            background: $theme-color;
        }
        .box-title {
            font-size: 18px;
            font-weight: 500;
            color: $white-color;
            margin-bottom: 10px;
        }
        .box-link {
            font-size: 28px;
            font-weight: 600;
            color: $white-color;
            margin-bottom: -0.1em;
            display: block;
        }
    }
    @include vxs {
        .widget-banner {
            display: block;
            padding: 10px 0;
            .icon-btn {
                margin-bottom: 20px;
            }
        }
    }
}
.widget_team_info {
    border: 0;
    padding: 0 !important;
    border-radius: 16px;
    overflow: hidden;
    background: $smoke-color;
    .widget-thumb {
        img {
            width: 100%;
        }
    }
    .widget-info-wrap {
        background: $title-color;
        border-radius: 3px;
        padding: 25px 48px;
        .box-title {
            color: $white-color;
            font-size: 28px;
            font-weight: 600;
            margin-bottom: -0.1em;
        }
        .box-text {
            color: $white-color;
            font-size: 18px;
            font-weight: 400;
        }
    }
}
.widget-contact-wrap {
    padding: 56px;
    position: relative;
    z-index: 1;
    .widget-logo {
        margin-bottom: 120px;
    }
    .widget_title {
        border: 0;
        color: $white-color;
        font-size: 40px;
        font-weight: 600;
        padding-bottom: 0;        
        margin-bottom: 58px;
        &:after {
            display: none;
        }
    }
    .widget-contact-list {
        list-style: none;
        padding: 0;
        margin: 0;
        li {
            display: flex;
            gap: 24px;
            align-items: center;
            &:not(:last-child) {
                margin-bottom: 48px;
            }
            .box-icon {
                flex: none;
                width: 70px;
                height: 70px;
                line-height: 70px;
                border: 1px solid $light-color;
                border-radius: 50%;
                color: $theme-color;
                text-align: center;
                font-size: 30px;
            }
            .box-title {
                font-size: 24px;
                font-weight: 600;
                color: $white-color;
                margin-bottom: 3px;
            }
            .box-text {
                color: $light-color;
            }
        }
    }
    @include ml {
        .widget-logo {
            margin-bottom: 60px;
        }
    }
    @include xl {
        padding: 40px 30px;
        .widget-logo {
            margin-bottom: 40px;
        }
        .widget_title {
            margin-bottom: 30px;
            font-size: 24px;
        }
        .widget-contact-list li .box-icon {
            width: 56px;
            height: 56px;
            line-height: 56px;
            font-size: 20px;
        }
    }
    @include vxs {
        .widget-contact-list li {
            display: block;
            .box-icon {
                margin-bottom: 20px;
            }
        }
    }
}
@media (min-width: 1200px) {
    .sidebar-shop {
        .widget {
            padding: 30px;
        }
    }
}

@media (min-width: 992px) {
    .sidebar-shop {
        .widget {
            margin-bottom: 24px;
        }
    }
}

@include lg {
    .widget {
        --widget-padding-y: 30px;
        --widget-padding-x: 30px;
    }
    .widget_title {
        font-size: 22px;
        margin: -0.12em 0 28px 0;
    }
}

@include md {
    .sidebar-area {
        padding-top: 30px;
    }
    .widget {
        --widget-padding-y: 40px;
        --widget-padding-x: 40px;
    }
}

@include sm {
    .widget {
        padding: 30px;
    }
}

@include vxs {
    .recent-post {
        .post-title {
            font-size: 16px;
            line-height: 24px;
        }
        .recent-post-meta a {
            font-size: 12px;
        }
    }
}
@media (max-width: 330px) {
    .recent-post {
        .media-img {
            margin-right: 15px;
        }
        .post-title {
            font-size: 14px;
            line-height: 24px;
        }
    }
}