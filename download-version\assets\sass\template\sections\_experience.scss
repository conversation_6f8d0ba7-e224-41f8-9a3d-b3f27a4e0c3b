/* Experience Area 1---------------------------------- */
.experience-card-wrap {
	--card-space: 60px;
	border-bottom: 1px solid rgba(255, 255, 255, 0.1);
	padding-bottom: var(--card-space);
	margin-bottom: var(--card-space);
	&:nth-last-child(2),
	&:last-child {
		margin-bottom: 0;
	}
	&:nth-child(2),
	&:first-child {
		border-top: 1px solid rgba(255, 255, 255, 0.1);
		padding-top: var(--card-space);
	}
	@include md {
		&:nth-child(2) {
			border-top: 0;
			padding-top: 0;
		}
		&:nth-last-child(2) {
			margin-bottom: var(--card-space);
		}
	}
	@include xs {
		--card-space: 45px;
	}
}
.experience-card {
	max-width: 685px;
	margin-right: 30px;
	.experience-meta {
		font-size: 18px;
		font-weight: 500;
		margin-top: -0.3em;
		display: flex;
		flex-wrap: wrap;
		margin-bottom: 42px;
		span,
		a {
			color: $light-color;
			position: relative;
			margin-right: 16px;
			&:after {
				content: '';
				display: inline-block;
				height: 13px;
				width: 3px;
				background-color: $theme-color;
				margin-left: 16px;
				position: relative;
				top: 0px;
			}
			&:last-child {
				margin-right: 0;
				&:after {
					display: none;
				}
			}
		}
		a {
			&:hover {
				color: $theme-color;
			}
		}
	}
	.box-title {
		color: $white-color;
		font-size: 28px;
		font-weight: 600;
	}
	.box-text {
		color: $light-color;
	}
	@include lg {
		.experience-meta {
			margin-bottom: 30px;
			span, 
			a {
				font-size: 16px;
			}
		}
		.box-title {
			font-size: 24px;
		}
	}
	@include md {
		max-width: none;
		margin-right: 0;
	}
}