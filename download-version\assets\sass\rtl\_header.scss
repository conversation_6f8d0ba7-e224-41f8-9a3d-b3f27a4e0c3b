/****header-default****/
.header-default .header-notice {
    margin-right: auto;
    margin-left: 0;
}
.main-menu ul li.menu-item-has-children > a,
.main-menu ul li:has(.mega-menu) > a,
.main-menu ul li:has(.sub-menu) > a {
    &:after {
        margin-left: 0px;
        margin-right: 5px;
        transform: rotate(0deg);
    }
    &:hover{
        &:after {
            transform: rotate(360deg);
        }
    }
}
.main-menu > ul > li:first-child {
    margin-left: 15px !important;
    margin-right: 0;
}
.main-menu > ul > li:last-child {
    margin-right: 15px !important;
    margin-left: 0;
}
.header-default .menu-area .menu-area-wrap {
    padding: 0 30px 0 18px;
    @include vxs {
        padding: 0;
    }
}
.info-card-wrap {
    .info-card {
        border-right: 2px solid $border-color;
        padding-right: 40px;
        padding-left: 0;
        border-left: 0;
        &:first-child {
            border-right: 0;
            padding-right: 0;
        }
        @include lg {
            border: 0 !important;
            padding-right: 0;
        }
    }
    @include lg {
        gap: 20px;
    }
    @include md {
        .info-card .box-title {
            font-size: 16px;
        }
    }
}

/****header-layout1****/
.header-layout1 {
    .header-notice {
        margin-right: auto;
        margin-left: 0;
    }
    .header-logo {
        margin-left: 80px;
        margin-right: 0;
    }
    .menu-area {
        .menu-area-wrap {
            padding: 0 30px 0 18px;
            @include xxl {
                padding: 0;
            }
        }
    }
}
.video-bg-shape2-1 {
    transform: rotateY(180deg);
}

/****header-layout2****/
.header-layout2 {
    .header-notice {
        margin-right: auto;
        margin-left: 0;
    }
    .header-logo {
        margin-left: 80px;
        margin-right: 0;
        @include xl {
            margin-left: 30px;
        }
        @include xs {
            margin-left: 0;
        }
    }
    .header-button {
        margin-right: 10px;
        margin-left: 0;
        @include xl {
            margin-right: 0;
        }
    }
    .menu-area {
        .menu-area-wrap {
            padding: 0 30px 0 18px;
            @include xxl {
                padding: 0;
            }
        }
    }
}