.preloader {
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: 999;
    background-color: $white-color;

    .th-btn {
        padding: 15px 20px;
        border-radius: 0;
        font-size: 14px;
    }
}

.preloader-inner {
    text-align: center;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    line-height: 1;

    img {
        display: block;
        margin: 0 auto 0 auto;
    }
}
.loader {
  font-size: 80px;
  display: inline-block;
  font-family: $title-font;
  font-weight: 700;
  color: $title-color;
  box-sizing: border-box;
  text-shadow: 0 0 2px $theme-color, 0 0 1px $theme-color, 0 0 1px $theme-color;
  position: relative;
  line-height: normal;
}
.loading-text {
  position: absolute;
  left: 0;
  top: 0;
  color: $theme-color;
  width: 100%;
  height: 100%;
  overflow: hidden;
  box-sizing: border-box;
  animation: animloader 6s linear infinite;
}

@keyframes animloader {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}