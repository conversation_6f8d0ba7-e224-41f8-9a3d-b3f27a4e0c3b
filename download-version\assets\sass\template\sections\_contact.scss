
/* Contact Area 1 ---------------------------------- */ 
.contact-thumb1-1 {
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 45%;
    img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    @include lg {
        position: relative;
        width: 100%;
        top: -150px;
    }
    @include md {
        top: -80px;
    }
}
.contact-form.style-border {
    border: 1px solid $border-color;
    border-radius: 50px;
    padding: 60px;
    @include md {
        border-radius: 40px;
        padding: 40px;
    }
    @include xs {
        padding: 30px;
    }
}
/* Contact Area 2 ---------------------------------- */ 
.contact-title-wrap {
    max-width: 713px;
}
.contact-thumb2-1 {
    @include lg {
        display: none;
    }
}
.contact-form-v2 {
    background: $title-color;
    padding: 56px;
    .title-area {
        .sub-title {
            font-size: 18px;
            padding-bottom: 0;
            margin-bottom: 15px;
        }
        .title {
            font-size: 40px;
            @include lg {
                font-size: 36px;
            }
            @include sm {
                font-size: 34px;
            }
            @include lg {
                font-size: 28px;
            }
        }
    }
    @include xs {
        padding: 40px 30px;
        .title-area .sub-title {
            font-size: 16px;
        }
    }
}
.contact-info {
    display: flex;
    gap: 32px;
    align-items: center;
    background: $title-color;
    padding: 32px 24px;
    .box-icon {
        flex: none;
        border: 1px solid rgba($color: #57585F, $alpha: 0.5);
        align-self: self-start;
        position: relative;
        height: 80px;
        width: 80px;
        justify-content: center;
        display: flex;
        align-items: center;
        z-index: 1;
        &:after {
            content: '';
            position: absolute;
            inset: 7px;
            background: $black-color2;
            z-index: -1;
        } 
    }
    .box-title {
        color: $white-color;
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 0;
    }
    .box-text {
        font-size: 16px;
        color: $light-color;
        margin-top: 10px;
        line-height: 1.4;
        a {
            color: $light-color;
            &:hover {
                color: $theme-color;
            }
        }
    }
    @include md {
        .box-title {
            font-size: 24px;
        }
    }
    @include xs {        
        display: block;
        .box-icon {
            margin-bottom: 25px;
        }
        .box-text {
            margin-bottom: -0.3em;
        }
        .box-title {
            font-size: 24px;
        }
    }
}
.contact-info-wrap2 {
    display: flex;
    justify-content: space-between;
    background: $title-color;
    flex-wrap: wrap;
    gap: 30px 30px;
    padding: 40px;
    margin: 0 -40px;
    margin-top: -160px;
    position: relative;
    z-index: 1;
    .contact-info {
        width: calc(25% - 30px);
        padding: 0;
    }
    @include xxl {
        margin: -160px 0 0;
        .contact-info {
            width: calc(50% - 30px);
        }
    }
    @include lg {
        margin-top: 60px;
    }
    @include md {
        .contact-info {
            width: 100%;
        }
    }
    @include xs {
        gap: 40px 30px;
        padding: 40px 30px;
        margin-top: 40px;
    }
}

/* Contact Area 4 ---------------------------------- */ 
.contact-form-v3 {
    background: $title-color;
    padding: 48px;
    border: 1px solid $body-color;
    border-radius: 8px;
    .title-area {
        .sub-title {
            font-size: 18px;
            padding-bottom: 0;
            margin-bottom: 15px;
        }
        .title {
            font-size: 40px;
            @include lg {
                font-size: 36px;
            }
            @include sm {
                font-size: 34px;
            }
            @include lg {
                font-size: 28px;
            }
        }
    }
    @include xs {
        padding: 40px 30px;
        .title-area .sub-title {
            font-size: 16px;
        }
    }
}
.contact-info.style2 {
    background: transparent;
    padding: 0;
    gap: 24px;
    .box-icon {
        border: 1px solid $white-color;
        border-radius: 8px;
        &:after {
            display: none;
        }
    }
}

/* Team Details Contact Form ---------------------------------- */ 
.contact-form-v4 {
    padding: 56px;
    @include xs {
        padding: 40px 30px;
    }
}

/* Contact Page ---------------------------------- */ 
.contact-page-v1 {
    position: relative;
    .contact-map {
        position: absolute;
        inset: 0;
        z-index: -1;
        iframe {
            height: 100%;
        }
    }
}
.contact-page-contact-form-wrap {
    display: flex;
    background: $smoke-color;
    border-radius: 16px;
    overflow: hidden;
    .contact-form-wrap {
        flex: 1;
        align-self: center;
        padding: 56px;
    }
    .contact-page-thumb1-1 {
        flex: 1;
        img {
            height: 100%;
            object-fit: cover;
        }
    }
    @include md {
        display: block;
    }
    @include xs {
        .contact-form-wrap {
            padding: 40px 30px;
        }
    }
}